"""2.1.0

Revision ID: ca5461f314f2
Revises: 55390f1f77c1
Create Date: 2025-02-06 18:28:00.644571

"""
import contextlib

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'ca5461f314f2'
down_revision = '55390f1f77c1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 订阅增加mediaid
    with contextlib.suppress(Exception):
        op.add_column('subscribe', sa.Column('mediaid', sa.String(), nullable=True))
        op.create_index('ix_subscribe_mediaid', 'subscribe', ['mediaid'], unique=False)
        op.add_column('subscribehistory', sa.Column('mediaid', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
