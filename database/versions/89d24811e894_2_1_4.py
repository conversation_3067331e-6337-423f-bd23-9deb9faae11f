"""2.1.4

Revision ID: 89d24811e894
Revises: 4b544f5d3b07
Create Date: 2025-05-03 17:29:07.635618

"""

from app.db.systemconfig_oper import SystemConfigOper
from app.schemas.types import SystemConfigKey

# revision identifiers, used by Alembic.
revision = '89d24811e894'
down_revision = '4b544f5d3b07'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    value = {
        "organizeSuccess": """
{
    'title': '{{ title_year }}'
            '{% if season_episode %} {{ season_episode }}{% endif %} 已入库',
    'text': '{% if vote_average %}评分：{{ vote_average }}，{% endif %}'
            '类型：{{ type }}'
            '{% if category %}，类别：{{ category }}{% endif %}'
            '{% if resource_term %}，质量：{{ resource_term }}{% endif %}，'
            '共{{ file_count }}个文件，大小：{{ total_size }}'
            '{% if err_msg %}，以下文件处理失败：{{ err_msg }}{% endif %}'
}""",
        "downloadAdded": """
{
    'title': '{{ title_year }}'
            '{% if download_episodes %} {{ season_fmt }} {{ download_episodes }}{% else %}{{ season_episode }}{% endif %} 开始下载',
    'text': '{% if site_name %}站点：{{ site_name }}{% endif %}'
            '{% if resource_term %}\\n质量：{{ resource_term }}{% endif %}'
            '{% if size %}\\n大小：{{ size }}{% endif %}'
            '{% if torrent_title %}\\n种子：{{ torrent_title }}{% endif %}'
            '{% if pubdate %}\\n发布时间：{{ pubdate }}{% endif %}'
            '{% if freedate %}\\n免费时间：{{ freedate }}{% endif %}'
            '{% if seeders %}\\n做种数：{{ seeders }}{% endif %}'
            '{% if volume_factor %}\\n促销：{{ volume_factor }}{% endif %}'
            '{% if hit_and_run %}\\nHit&Run：{{ hit_and_run }}{% endif %}'
            '{% if labels %}\\n标签：{{ labels }}{% endif %}'
            '{% if description %}\\n描述：{{ description }}{% endif %}'
}""",
        "subscribeAdded": "{'title': '{{ title_year }}{% if season_fmt %} {{ season_fmt }}{% endif %} 已添加订阅'}",
        "subscribeComplete": """
{
    'title': '{{ title_year }}'
            '{% if season_fmt %} {{ season_fmt }}{% endif %} 已完成{{ msgstr }}',
    'text': '{% if vote_average %}评分：{{ vote_average }}{% endif %}'
            '{% if username %}，来自用户：{{ username }}{% endif %}'
            '{% if actors %}\\n演员：{{ actors }}{% endif %}'
            '{% if overview %}\\n简介：{{ overview }}{% endif %}'
}"""
    }
    _systemconfig = SystemConfigOper()
    if not _systemconfig.get(SystemConfigKey.NotificationTemplates):
        _systemconfig.set(SystemConfigKey.NotificationTemplates, value)

    # ### end Alembic commands ###


def downgrade() -> None:
    pass
