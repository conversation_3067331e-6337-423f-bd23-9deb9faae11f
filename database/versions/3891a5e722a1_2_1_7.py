"""2.1.7

Revision ID: 3891a5e722a1
Revises: 3df653756eec
Create Date: 2025-06-28 08:40:14.516836

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

from app.db.systemconfig_oper import SystemConfigOper
from app.schemas.types import SystemConfigKey

# revision identifiers, used by Alembic.
revision = '3891a5e722a1'
down_revision = '3df653756eec'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # rename AList存储
    _systemconfig = SystemConfigOper()
    _storages = _systemconfig.get(SystemConfigKey.Storages)
    if _storages:
        for storage in _storages:
            if storage["type"] == "alist":
                storage["name"] = "OpenList"
                break
        _systemconfig.set(SystemConfigKey.Storages, _storages)
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
