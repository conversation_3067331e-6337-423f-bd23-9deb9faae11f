"""2.0.2

Revision ID: 0fb94bf69b38
Revises: 262735d025da
Create Date: 2024-09-30 10:03:58.546036

"""
import contextlib

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0fb94bf69b38'
down_revision = '262735d025da'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 站点数据统计增加站点名称
    with contextlib.suppress(Exception):
        op.add_column('siteuserdata', sa.Column('name', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
