"""2.0.4

Revision ID: a73f2dbf5c09
Revises: e2dbe1421fa4
Create Date: 2024-10-16 15:05:01.775429

"""

from app.db.systemconfig_oper import SystemConfigOper
from app.schemas.types import SystemConfigKey

# revision identifiers, used by Alembic.
revision = 'a73f2dbf5c09'
down_revision = 'e2dbe1421fa4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 初始化下载优先规则
    SystemConfigOper().set(SystemConfigKey.TorrentsPriority, ["torrent", "upload", "seeder"])
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
