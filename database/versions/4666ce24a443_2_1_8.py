"""2.1.8

Revision ID: 4666ce24a443
Revises: 3891a5e722a1
Create Date: 2025-07-22 13:54:04.196126

"""
import contextlib

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '4666ce24a443'
down_revision = '3891a5e722a1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with contextlib.suppress(Exception):
        # 添加触发类型字段
        op.add_column('workflow', sa.Column('trigger_type', sa.String(), nullable=True, default='timer'))

    with contextlib.suppress(Exception):
        # 添加事件类型字段
        op.add_column('workflow', sa.Column('event_type', sa.String(), nullable=True))

    with contextlib.suppress(Exception):
        # 添加事件条件字段
        op.add_column('workflow', sa.Column('event_conditions', sa.JSON(), nullable=True, default={}))
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
