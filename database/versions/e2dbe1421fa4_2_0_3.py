"""2.0.3

Revision ID: e2dbe1421fa4
Revises: 0fb94bf69b38
Create Date: 2024-10-09 13:44:13.926529

"""
import contextlib

from alembic import op
import sqlalchemy as sa

from app.db import SessionFactory
from app.db.models import UserConfig

# revision identifiers, used by Alembic.
revision = 'e2dbe1421fa4'
down_revision = '0fb94bf69b38'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 支持订阅自定义媒体类别和过滤规则组、自定义识别词
    with contextlib.suppress(Exception):
        op.add_column('downloadhistory', sa.Column('media_category', sa.String(), nullable=True))
        op.add_column('subscribe', sa.Column('custom_words', sa.String(), nullable=True))
        op.add_column('subscribe', sa.Column('media_category', sa.String(), nullable=True))
        op.add_column('subscribe', sa.Column('filter_groups', sa.JSON(), nullable=True))
    # 将String转换为JSON类型
    with contextlib.suppress(Exception):
        op.alter_column('subscribe', 'note', existing_type=sa.String(), type_=sa.JSON())
        op.alter_column('downloadhistory', 'note', existing_type=sa.String(), type_=sa.JSON())
        op.alter_column('mediaserveritem', 'note', existing_type=sa.String(), type_=sa.JSON())
        op.alter_column('message', 'note', existing_type=sa.String(), type_=sa.JSON())
        op.alter_column('plugindata', 'value', existing_type=sa.String(), type_=sa.JSON())
        op.alter_column('site', 'note', existing_type=sa.String(), type_=sa.JSON())
        op.alter_column('sitestatistic', 'note', existing_type=sa.String(), type_=sa.JSON())
        op.alter_column('systemconfig', 'value', existing_type=sa.String(), type_=sa.JSON())
        op.alter_column('userconfig', 'value', existing_type=sa.String(), type_=sa.JSON())
    # 清空用户配置表中不兼容的数据
    with SessionFactory() as db:
        UserConfig.truncate(db)
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
