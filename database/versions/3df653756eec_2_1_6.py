"""2.1.6

Revision ID: 3df653756eec
Revises: 486e56a62dcb
Create Date: 2025-06-11 19:52:57.185355

"""
import json

from app.db import SessionFactory
from app.db.models import User

# revision identifiers, used by Alembic.
revision = '3df653756eec'
down_revision = '486e56a62dcb'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with SessionFactory() as db:
        # 所有用户
        users = User.list(db)
        for user in users:
            if user.is_superuser:
                continue
            if not user.permissions:
                permissions = {
                    "discovery": True,
                    "search": True,
                    "subscribe": True,
                    "manage": False,
                }
                user.update(db, {
                    "permissions": permissions,
                })
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
