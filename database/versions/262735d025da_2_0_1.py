"""2.0.1

Revision ID: 262735d025da
Revises: 294b007932ef
Create Date: 2024-09-11 08:07:02.753307

"""

from app.db.systemconfig_oper import SystemConfigOper
from app.schemas.types import SystemConfigKey

# revision identifiers, used by Alembic.
revision = '262735d025da'
down_revision = '294b007932ef'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 初始化消息通知范围
    _systemconfig = SystemConfigOper()
    if not _systemconfig.get(SystemConfigKey.NotificationSwitchs):
        _systemconfig.set(SystemConfigKey.NotificationSwitchs, [
            {
                'type': '资源下载',
                'action': 'all',
            },
            {
                'type': '整理入库',
                'action': 'all',
            },
            {
                'type': '订阅',
                'action': 'all',
            },
            {
                'type': '站点',
                'action': 'admin',
            },
            {
                'type': '媒体服务器',
                'action': 'admin',
            },
            {
                'type': '手动处理',
                'action': 'admin',
            },
            {
                'type': '插件',
                'action': 'admin',
            },
            {
                'type': '其它',
                'action': 'admin',
            },
        ])
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
