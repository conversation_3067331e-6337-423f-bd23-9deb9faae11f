Cython~=3.1.2
pydantic~=1.10.22
SQLAlchemy~=2.0.41
uvicorn~=0.34.3
fastapi~=0.115.14
passlib~=1.7.4
PyJWT~=2.10.1
python-multipart~=0.0.9
alembic~=1.16.2
bcrypt~=4.0.1
regex~=2024.11.6
cn2an~=0.5.19
dateparser~=1.2.2
python-dateutil~=2.8.2
zhconv~=1.4.3
anitopy~=2.1.1
requests[socks]~=2.32.4
urllib3~=2.5.0
lxml~=6.0.0
pyquery~=2.0.1
ruamel.yaml~=0.18.14
APScheduler~=3.11.0
cryptography~=45.0.4
pytz~=2025.2
pycryptodome~=3.23.0
qbittorrent-api==2025.5.0
plexapi~=4.17.0
transmission-rpc~=4.3.0
Jinja2~=3.1.6
pyparsing~=3.2.3
func_timeout==4.3.5
bs4~=0.0.2
beautifulsoup4~=4.13.4
pillow~=11.2.1
pillow-avif-plugin~=1.5.2
pyTelegramBotAPI~=4.27.0
playwright~=1.53.0
cf_clearance~=0.31.0
torrentool~=1.2.0
slack-bolt~=1.23.0
slack-sdk~=3.35.0
chardet~=5.2.0
starlette~=0.46.2
PyVirtualDisplay~=3.0
psutil~=7.0.0
python-dotenv~=1.1.1
python-hosts~=1.1.2
watchdog~=6.0.0
cacheout~=0.16.0
click~=8.2.1
requests-cache~=1.2.1
parse~=1.20.2
docker~=7.1.0
pywin32==310; platform_system == "Windows"
cachetools~=6.1.0
fast-bencode~=1.1.7
pystray~=0.19.5
pyotp~=2.9.0
Pinyin2Hanzi~=0.1.1
pywebpush~=2.0.3
python-cookietools==0.0.4
aiofiles~=24.1.0
jieba~=0.42.1
rsa~=4.9
redis~=6.2.0
async_timeout~=5.0.1; python_full_version < "3.11.3"
packaging~=25.0
oss2~=2.19.1
tqdm~=4.67.1
setuptools~=78.1.0
pympler~=1.1
smbprotocol~=1.15.0
setproctitle~=1.3.6
