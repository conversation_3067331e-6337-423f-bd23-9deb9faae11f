worker_processes 1;
user root;
daemon on;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    upstream docker {
        server unix:/var/run/docker.sock fail_timeout=0;
    }
    server {
        listen 38379;
        server_name localhost;

        access_log /dev/stdout combined;
        error_log /dev/stdout;

        location / {
            proxy_pass http://docker;
            proxy_redirect off;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            client_max_body_size 10m;
            client_body_buffer_size 128k;

            proxy_connect_timeout 90;
            proxy_send_timeout 120;
            proxy_read_timeout 120;

            proxy_buffer_size 4k;
            proxy_buffers 4 32k;
            proxy_busy_buffers_size 64k;
            proxy_temp_file_write_size 64k;
        }
    }
}
