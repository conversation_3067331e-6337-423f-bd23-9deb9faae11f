
# 公共根目录
root /public;

# 主应用路由
location / {
    expires off;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    try_files $uri $uri/ /index.html;
}

# 图片类静态资源
location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# assets目录
location /assets {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 站点图标
location /api/v1/site/icon/ {
    # 站点图标缓存
    proxy_cache my_cache;
    # 缓存响应码为200和302的请求1小时
    proxy_cache_valid 200 302 1h;
    # 缓存其他响应码的请求5分钟
    proxy_cache_valid any 5m;
    # 缓存键的生成规则
    proxy_cache_key "$scheme$request_method$host$request_uri";
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;

    # 向后端API转发请求
    proxy_pass http://backend_api;
}


# 本地CookieCloud
location /cookiecloud {
    proxy_pass http://backend_api;
    rewrite ^.+mock-server/?(.*)$ /$1 break;
    proxy_http_version 1.1;
    proxy_buffering off;
    proxy_cache off;
    proxy_redirect off;
    proxy_set_header Connection "";
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $http_host;
    proxy_set_header X-Nginx-Proxy true;

    # 超时设置
    proxy_read_timeout 600s;
}

# SSE特殊配置
location ~ ^/api/v1/system/(message|progress/) {
    # SSE MIME类型设置
    default_type text/event-stream;

    # 禁用缓存
    add_header Cache-Control no-cache;
    add_header X-Accel-Buffering no;
    proxy_buffering off;
    proxy_cache off;

    # 代理设置
    proxy_pass http://backend_api;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    # 超时设置
    proxy_read_timeout 3600s;
}

# API代理配置
location /api {
    proxy_pass http://backend_api;
    rewrite ^.+mock-server/?(.*)$ /$1 break;
    proxy_http_version 1.1;
    proxy_buffering off;
    proxy_cache off;
    proxy_redirect off;
    proxy_set_header Connection "";
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $http_host;
    proxy_set_header X-Nginx-Proxy true;

    # 超时设置
    proxy_read_timeout 600s;
}