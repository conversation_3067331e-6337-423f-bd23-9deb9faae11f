user moviepilot;
worker_processes auto;
worker_cpu_affinity auto;


events {
    worker_connections 1024;
}


http {
    # 设置缓存路径和缓存区大小
    proxy_cache_path /tmp levels=1:2 keys_zone=my_cache:10m max_size=100m inactive=60m use_temp_path=off;

    sendfile on;

    keepalive_timeout 3600;

    client_max_body_size ${NGINX_CLIENT_MAX_BODY_SIZE};

    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_proxied any;
    gzip_min_length 256;
    gzip_vary on;
    gzip_comp_level 6;

    # HTTP
    server {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        listen ${NGINX_PORT};
        listen [::]:${NGINX_PORT};
        server_name moviepilot;

        # 公共配置
        include common.conf;
    }

    # HTTPS
    ${HTTPS_SERVER_CONF}

    upstream backend_api {
        # 后端API的地址和端口
        server 127.0.0.1:${PORT};
        # 可以添加更多后端服务器作为负载均衡
    }

}
