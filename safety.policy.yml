security:
  ignore-unpinned-requirements: False
  ignore-vulnerabilities:
    70612:
      reason: The official statement indicates that this vulnerability is not valid because users should use sandboxing when handling untrusted templates.
    65532:
      reason: Legacy issue related to tvdbapi usage.
    40100:
      reason: Legacy issue related to tvdbapi usage.
    68094:
      reason: This vulnerability is resolved by upgrading `python-multipart` to version 0.0.9.
    65293:
      reason: This vulnerability is resolved by upgrading `python-multipart` to version 0.0.9.
    64930:
      reason: This vulnerability is resolved by upgrading `python-multipart` to version 0.0.9.
