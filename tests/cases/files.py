#!/usr/bin/env python
# -*- coding:utf-8 -*-
bluray_files = [
    {
        "name": "FOLDER",
        "children": [
            {
                "name": "<PERSON><PERSON><PERSON>",
                "children": [
                    {
                        "name": "<PERSON><PERSON><PERSON> (2055)",
                        "children": [
                            {
                                "name": "BDMV",
                                "children": [
                                    {
                                        "name": "STREAM",
                                        "children": [
                                            {
                                                "name": "00000.m2ts",
                                                "size": 104857600,
                                            },
                                            {
                                                "name": "00001.m2ts",
                                                "size": 104857600,
                                            },
                                        ],
                                    },
                                ],
                            },
                            {
                                "name": "CERTIFICATE",
                                "children": [],
                            },
                        ],
                    },
                    {
                        "name": "<PERSON><PERSON><PERSON> (2099)",
                        "children": [
                            {
                                "name": "BDMV",
                                "children": [
                                    {
                                        "name": "STREAM",
                                        "children": [
                                            {
                                                "name": "00000.m2ts",
                                                "size": 104857600,
                                            },
                                            {
                                                "name": "00001.m2ts",
                                                "size": 104857600,
                                            },
                                            {
                                                "name": "00002.m2ts.!qB",
                                                "size": 104857600,
                                            },
                                        ],
                                    },
                                ],
                            },
                            {
                                "name": "CERTIFICATE",
                                "children": [],
                            },
                        ],
                    },
                    {
                        "name": "Digimon (2199)",
                        "children": [
                            {
                                "name": "Digimon.2199.mp4",
                                "size": 104857600,
                            },
                        ],
                    },
                ],
            },
            {
                "name": "Pokemon (2016)",
                "children": [
                    {
                        "name": "BDMV",
                        "children": [
                            {
                                "name": "STREAM",
                                "children": [
                                    {
                                        "name": "00000.m2ts",
                                        "size": 104857600,
                                    },
                                    {
                                        "name": "00001.m2ts",
                                        "size": 104857600,
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        "name": "CERTIFICATE",
                        "children": [],
                    },
                ],
            },
            {
                "name": "Pokemon (2021)",
                "children": [
                    {
                        "name": "BDMV",
                        "children": [
                            {
                                "name": "STREAM",
                                "children": [
                                    {
                                        "name": "00000.m2ts",
                                        "size": 104857600,
                                    },
                                    {
                                        "name": "00001.m2ts",
                                        "size": 104857600,
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        "name": "CERTIFICATE",
                        "children": [],
                    },
                ],
            },
            {
                "name": "Pokemon (2028)",
                "children": [
                    {
                        "name": "Pokemon.2028.mkv",
                        "size": 104857600,
                    },
                    {
                        "name": "Pokemon.2028.hdr.mkv.!qB",
                        "size": 104857600,
                    },
                ],
            },
            {
                "name": "Pokemon.2029.mp4",
                "size": 104857600,
            },
            {
                "name": "Pokemon (2030)",
                "children": [
                    {
                        "name": "S",
                        "size": 104857600,
                    },
                ],
            },
        ],
    },
]
