meta_cases = [{
    "title": "The Long Season 2017 2160p WEB-DL H265 AAC-XXX",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "The Long Season",
        "year": "2017",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "WEB-DL",
        "pix": "2160p",
        "video_codec": "H265",
        "audio_codec": "AAC"
    }
}, {
    "title": "Cherry Season S01 2014 2160p WEB-DL H265 AAC-XXX",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Cherry Season",
        "year": "2014",
        "part": "",
        "season": "S01",
        "episode": "",
        "restype": "WEB-DL",
        "pix": "2160p",
        "video_codec": "H265",
        "audio_codec": "AAC"
    }
}, {
    "title": "【爪爪字幕组】★7月新番[欢迎来到实力至上主义的教室 第二季/Youkoso Jitsuryoku Shijou Shugi no Kyoushitsu e S2][11][1080p][HEVC][GB][MP4][招募翻译校对]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "欢迎来到实力至上主义的教室",
        "en_name": "Youkoso Jitsuryoku Shijou Shugi No Kyoushitsu E",
        "year": "",
        "part": "",
        "season": "S02",
        "episode": "E11",
        "restype": "",
        "pix": "1080p",
        "video_codec": "HEVC",
        "audio_codec": ""
    }
}, {
    "title": "National.Parks.Adventure.AKA.America.Wild:.National.Parks.Adventure.3D.2016.1080p.Blu-ray.AVC.TrueHD.7.1",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "National Parks Adventure",
        "year": "2016",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "BluRay 3D",
        "pix": "1080p",
        "video_codec": "AVC",
        "audio_codec": "TrueHD 7.1"
    }
}, {
    "title": "[秋叶原冥途战争][Akiba Maid Sensou][2022][WEB-DL][1080][TV Series][第01话][LeagueWEB]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Akiba Maid Sensou",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E01",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "哆啦A梦：大雄的宇宙小战争 2021 (2022) - 1080p.mp4",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "哆啦A梦：大雄的宇宙小战争 2021",
        "en_name": "",
        "year": "2022",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "新精武门1991 (1991).mkv",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "新精武门1991",
        "en_name": "",
        "year": "1991",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "24 S01 1080p WEB-DL AAC2.0 H.264-BTN",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "24",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "AAC 2.0"
    }
}, {
    "title": "Qi Refining for 3000 Years S01E06 2022 1080p B-Blobal WEB-DL X264 AAC-AnimeS@AdWeb",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Qi Refining For 3000 Years",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E06",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "x264",
        "audio_codec": "AAC"
    }
}, {
    "title": "Noumin Kanren no Skill Bakka Agetetara Naze ka Tsuyoku Natta S01E02 2022 1080p B-Global WEB-DL X264 AAC-AnimeS@ADWeb[2022年10月新番]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Noumin Kanren No Skill Bakka Agetetara Naze Ka Tsuyoku Natta",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E02",
        "restype": "B-Global WEB-DL",
        "pix": "1080p",
        "video_codec": "x264",
        "audio_codec": "AAC"
    }
}, {
    "title": "dou luo da lu S01E229 2018 2160p WEB-DL H265 AAC-ADWeb[[国漫连载] 斗罗大陆 第229集 4k | 国语中字]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Dou Luo Da Lu",
        "year": "2018",
        "part": "",
        "season": "S01",
        "episode": "E229",
        "restype": "WEB-DL",
        "pix": "2160p",
        "video_codec": "H265",
        "audio_codec": "AAC"
    }
}, {
    "title": "Thor Love and Thunder (2022) [1080p] [WEBRip] [5.1]",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "Thor Love And Thunder",
        "year": "2022",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "5.1"
    }
}, {
    "title": "[Animations(动画片)][[诛仙][Jade Dynasty][2022][WEB-DL][2160][TV Series][TV 08][LeagueWEB]][诛仙/诛仙动画 第一季 第08集 | 类型:动画 [国语中字]][680.12 MB]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Jade Dynasty",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E08",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "钢铁侠2 (2010) 1080p AC3.mp4",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "钢铁侠2",
        "en_name": "",
        "year": "2010",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "AC3"
    }
}, {
    "title": "Wonder Woman 1984 2020 BluRay 1080p Atmos TrueHD 7.1 X264-EPiC",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "Wonder Woman 1984",
        "year": "2020",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "BluRay",
        "pix": "1080p",
        "video_codec": "x264",
        "audio_codec": "Atmos TrueHD 7.1"
    }
}, {
    "title": "9-1-1 - S04E03 - Future Tense WEBDL-1080p.mp4",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "9 1 1",
        "year": "",
        "part": "",
        "season": "S04",
        "episode": "E03",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "【幻月字幕组】【22年日剧】【据幸存的六人所说】【04】【1080P】【中日双语】",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "据幸存的六人所说",
        "en_name": "",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E04",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "【爪爪字幕组】★7月新番[即使如此依旧步步进逼/Soredemo Ayumu wa Yosetekuru][09][1080p][HEVC][GB][MP4][招募翻译校对]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Soredemo Ayumu Wa Yosetekuru",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E09",
        "restype": "",
        "pix": "1080p",
        "video_codec": "HEVC",
        "audio_codec": ""
    }
}, {
    "title": "[猎户不鸽发布组] 不死者之王 第四季 OVERLORD Ⅳ [02] [1080p] [简中内封] [2022年7月番]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "不死者之王",
        "en_name": "Overlord Ⅳ",
        "year": "",
        "part": "",
        "season": "S04",
        "episode": "E02",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "[GM-Team][国漫][寻剑 第1季][Sword Quest Season 1][2002][02][AVC][GB][1080P]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Sword Quest",
        "year": "2002",
        "part": "",
        "season": "S01",
        "episode": "E02",
        "restype": "",
        "pix": "1080p",
        "video_codec": "AVC",
        "audio_codec": ""
    }
}, {
    "title": " [猎户不鸽发布组] 组长女儿与照料专员 / 组长女儿与保姆 Kumichou Musume to Sewagakari [09] [1080p+] [简中内嵌] [2022年7月番]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "组长女儿与保姆",
        "en_name": "Kumichou Musume To Sewagakari",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E09",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "Nande Koko ni Sensei ga!? 2019 Blu-ray Remux 1080p AVC LPCM-7³ ACG",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "Nande Koko Ni Sensei Ga!?",
        "year": "2019",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "BluRay REMUX",
        "pix": "1080p",
        "video_codec": "AVC",
        "audio_codec": "LPCM 7³"
    }
}, {
    "title": "30.Rock.S02E01.1080p.UHD.BluRay.X264-BORDURE.mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "30 Rock",
        "year": "",
        "part": "",
        "season": "S02",
        "episode": "E01",
        "restype": "UHD BluRay",
        "pix": "1080p",
        "video_codec": "x264",
        "audio_codec": ""
    }
}, {
    "title": "[Gal to Kyouryuu][02][BDRIP][1080P][H264_FLAC].mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Gal To Kyouryuu",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E02",
        "restype": "",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "FLAC"
    }
}, {
    "title": "[AI-Raws] 逆境無頼カイジ #13 (BD HEVC 1920x1080 yuv444p10le FLAC)[7CFEE642].mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "逆境無頼カイジ",
        "en_name": "",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E13",
        "restype": "BD",
        "pix": "1080p",
        "video_codec": "HEVC",
        "audio_codec": "FLAC"
    }
}, {
    "title": "Mr. Robot - S02E06 - eps2.4_m4ster-s1ave.aes SDTV.mp4",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Mr Robot",
        "year": "",
        "part": "",
        "season": "S02",
        "episode": "E06",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "[神印王座][Throne of Seal][2022][WEB-DL][2160][TV Series][TV 22][LeagueWEB] 神印王座 第一季 第22集 | 类型:动画 [国语中字][967.44 MB]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Throne Of Seal",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E22",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "S02E1000.mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "",
        "year": "",
        "part": "",
        "season": "S02",
        "episode": "E1000",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "西部世界 12.mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "西部世界",
        "en_name": "",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E12",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "[ANi] OVERLORD 第四季 - 04 [1080P][Baha][WEB-DL][AAC AVC][CHT].mp4",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Overlord",
        "year": "",
        "part": "",
        "season": "S04",
        "episode": "E04",
        "restype": "",
        "pix": "1080p",
        "video_codec": "AVC",
        "audio_codec": "AAC"
    }
}, {
    "title": "[SweetSub&LoliHouse] Made in Abyss S2 - 03v2 [WebRip 1080p HEVC-10bit AAC ASSx2].mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Made In Abyss",
        "year": "",
        "part": "",
        "season": "S02",
        "episode": "E03",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "AAC"
    }
}, {
    "title": "[GM-Team][国漫][斗破苍穹 第5季][Fights Break Sphere V][2022][05][HEVC][GB][4K]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Fights Break Sphere V",
        "year": "2022",
        "part": "",
        "season": "S05",
        "episode": "E05",
        "restype": "",
        "pix": "2160p",
        "video_codec": "HEVC",
        "audio_codec": ""
    }
}, {
    "title": "Ousama Ranking S01E02-[1080p][BDRIP][X265.FLAC].mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Ousama Ranking",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E02",
        "restype": "BDRIP",
        "pix": "1080p",
        "video_codec": "x265",
        "audio_codec": "FLAC"
    }
}, {
    "title": "[Nekomoe kissaten&LoliHouse] Soredemo Ayumu wa Yosetekuru - 01v2 [WebRip 1080p HEVC-10bit EAC3 ASSx2].mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Soredemo Ayumu Wa Yosetekuru",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E01",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "EAC3"
    }
}, {
    "title": "[喵萌奶茶屋&LoliHouse] 金装的薇尔梅 / Kinsou no Vermeil - 01 [WebRip 1080p HEVC-10bit AAC][简繁内封字幕]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "金装的薇尔梅",
        "en_name": "Kinsou No Vermeil",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E01",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "AAC"
    }
}, {
    "title": "Hataraku.Maou-sama.S02E05.2022.1080p.CR.WEB-DL.X264.AAC-ADWeb.mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Hataraku Maou Sama",
        "year": "2022",
        "part": "",
        "season": "S02",
        "episode": "E05",
        "restype": "Crunchyroll WEB-DL",
        "pix": "1080p",
        "video_codec": "x264",
        "audio_codec": "AAC"
    }
}, {
    "title": "The Witch Part 2：The Other One 2022 1080p WEB-DL AAC5.1 H264-tG1R0",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "The Witch Part 2：The Other One",
        "year": "2022",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "AAC 5.1"
    }
}, {
    "title": "一夜新娘 - S02E07 - 第 7 集.mp4",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "一夜新娘",
        "en_name": "",
        "year": "",
        "part": "",
        "season": "S02",
        "episode": "E07",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "[ANi] 處刑少女的生存之道 - 07 [1080P][Baha][WEB-DL][AAC AVC][CHT].mp4",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "處刑少女的生存之道",
        "en_name": "",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E07",
        "restype": "",
        "pix": "1080p",
        "video_codec": "AVC",
        "audio_codec": "AAC"
    }
}, {
    "title": "Stand-up.Comedy.S01E01.PartA.2022.1080p.WEB-DL.H264.AAC-TJUPT.mp4",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Stand Up Comedy",
        "year": "2022",
        "part": "PartA",
        "season": "S01",
        "episode": "E01",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "AAC"
    }
}, {
    "title": "教父3.The.Godfather.Part.III.1990.1080p.NF.WEBRip.H264.DDP5.1-PTerWEB.mkv",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "教父3",
        "en_name": "The Godfather Part Iii",
        "year": "1990",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "Netflix WEBRip",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "DDP 5.1"
    }
}, {
    "title": "A.Quiet.Place.Part.II.2020.1080p.UHD.BluRay.DD+7.1.DoVi.X265-PuTao",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "A Quiet Place Part Ii",
        "year": "2020",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "UHD BluRay DoVi",
        "pix": "1080p",
        "video_codec": "x265",
        "audio_codec": "DD+ 7.1"
    }
}, {
    "title": "Childhood.In.A.Capsule.S01E16.2022.1080p.KKTV.WEB-DL.X264.AAC-ADWeb.mkv",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Childhood In A Capsule",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E16",
        "restype": "KKTV WEB-DL",
        "pix": "1080p",
        "video_codec": "x264",
        "audio_codec": "AAC"
    }
}, {
    "title": "[桜都字幕组] 异世界归来的舅舅 / Isekai Ojisan [01][1080p][简体内嵌]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Isekai Ojisan",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E01",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "【喵萌奶茶屋】★04月新番★[夏日重現/Summer Time Rendering][15][720p][繁日雙語][招募翻譯片源]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Summer Time Rendering",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E15",
        "restype": "",
        "pix": "720p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "[NC-Raws] 打工吧！魔王大人 第二季 / Hataraku Maou-sama!! - 02 (B-Global 1920x1080 HEVC AAC MKV)",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "打工吧！魔王大人",
        "en_name": "Hataraku Maou-Sama!!",
        "year": "",
        "part": "",
        "season": "S02",
        "episode": "E02",
        "restype": "",
        "pix": "1080p",
        "video_codec": "HEVC",
        "audio_codec": "AAC"
    }
}, {
    "title": "The Witch Part 2 The Other One 2022 1080p WEB-DL AAC5.1 H.264-tG1R0",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "The Witch Part 2 The Other One",
        "year": "2022",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "AAC 5.1"
    }
}, {
    "title": "The 355 2022 BluRay 1080p DTS-HD MA5.1 X265.10bit-BeiTai",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "The 355",
        "year": "2022",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "BluRay",
        "pix": "1080p",
        "video_codec": "x265 10bit",
        "audio_codec": "DTS-HD MA 5.1"
    }
}, {
    "title": "Sense8 s01-s02 2015-2017 1080P WEB-DL X265 AC3￡cXcY@FRDS",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Sense8",
        "year": "2015",
        "part": "",
        "season": "S01-S02",
        "episode": "",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "x265",
        "audio_codec": ""
    }
}, {
    "title": "The Heart of Genius S01 13-14 2022 1080p WEB-DL H264 AAC",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "The Heart Of Genius",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E13-E14",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "AAC"
    }
}, {
    "title": "The Heart of Genius E13-14 2022 1080p WEB-DL H264 AAC",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "The Heart Of Genius",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E13-E14",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "AAC"
    }
}, {
    "title": "2022.8.2.Twelve.Monkeys.1995.GBR.4K.REMASTERED.BluRay.1080p.X264.DTS [3.4 GB]",
    "subtitle": "",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "Twelve Monkeys",
        "year": "1995",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "BluRay",
        "pix": "4k",
        "video_codec": "x264",
        "audio_codec": "DTS"
    }
}, {
    "title": "[NC-Raws] 王者天下 第四季 - 17 (Baha 1920x1080 AVC AAC MP4) [3B1AA7BB].mp4",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "王者天下",
        "en_name": "",
        "year": "",
        "part": "",
        "season": "S04",
        "episode": "E17",
        "restype": "",
        "pix": "1080p",
        "video_codec": "AVC",
        "audio_codec": "AAC"
    }
}, {
    "title": "Sense8 S2E1 2015-2017 1080P WEB-DL X265 AC3￡cXcY@FRDS",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Sense8",
        "year": "2015",
        "part": "",
        "season": "S02",
        "episode": "E01",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "x265",
        "audio_codec": ""
    }
}, {
    "title": "[xyx98]传颂之物/Utawarerumono/うたわれるもの[BDrip][1920x1080][TV 01-26 Fin][hevc-yuv420p10 flac_ac3][ENG PGS]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "うたわれるもの",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E01-E26",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "flac"
    }
}, {
    "title": "[云歌字幕组][7月新番][欢迎来到实力至上主义的教室 第二季][01][X264 10bit][1080p][简体中文].mp4",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "欢迎来到实力至上主义的教室",
        "en_name": "",
        "year": "",
        "part": "",
        "season": "S02",
        "episode": "E01",
        "restype": "",
        "pix": "1080p",
        "video_codec": "X264",
        "audio_codec": ""
    }
}, {
    "title": "[诛仙][Jade Dynasty][2022][WEB-DL][2160][TV Series][TV 04][LeagueWEB]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Jade Dynasty",
        "year": "2022",
        "part": "",
        "season": "S01",
        "episode": "E04",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "Rick and Morty.S06E06.JuRicksic.Mort.1080p.HMAX.WEBRip.DD5.1.X264-NTb[rartv]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Rick And Morty",
        "year": "",
        "part": "",
        "season": "S06",
        "episode": "E06",
        "restype": "Max WEBRip",
        "pix": "1080p",
        "video_codec": "x264",
        "audio_codec": "DD 5.1"
    }
}, {
    "title": "rick and Morty.S06E05.JuRicksic.Mort.1080p.HMAX.WEBRip.DD5.1.X264-NTb[rartv]",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Rick And Morty",
        "year": "",
        "part": "",
        "season": "S06",
        "episode": "E05",
        "restype": "Max WEBRip",
        "pix": "1080p",
        "video_codec": "x264",
        "audio_codec": "DD 5.1"
    }
}, {
    "title": "[Hall_of_C] 诛仙 Zhu Xian (Jade Dynasty) - Episode 19",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "诛仙",
        "en_name": "Zhu Xian Jade Dynasty",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E19",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "I Woke Up a Vampire S02 2023 2160p NF WEB-DL DDP5.1 Atmos H 265-HHWEB",
    "subtitle": "醒来变成吸血鬼 第二季 | 全8集 | 4K | 类型: 喜剧/家庭/奇幻 | 导演: TommyLynch | 主演: NikoCeci/ZebastinBorjeau/安娜·阿劳约/KaileenAngelicChang/KrisSiddiqi",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "I Woke Up A Vampire",
        "year": "2023",
        "part": "",
        "season": "S02",
        "episode": "",
        "restype": "Netflix WEB-DL",
        "pix": "2160p",
        "video_codec": "H265",
        "audio_codec": "DDP 5.1 Atmos"
    }
}, {
    "title": "Shadows of the Void S01 2024 1080p WEB-DL H264 AAC-HHWEB",
    "subtitle": "虚无边境 | 第01-02集 | 1080p | 类型: 动画 | 导演: 巴西 | 主演: 山新/周一菡/皇贞季/Kenz/李佳怡 [内嵌中字]",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Shadows Of The Void",
        "year": "2024",
        "part": "",
        "season": "S01",
        "episode": "E01-E02",
        "restype": "WEB-DL",
        "pix": "1080p",
        "video_codec": "H264",
        "audio_codec": "AAC"
    }
}, {
    "title": "【极影字幕社】★1月新番 Metallic Rouge/金属口红 第13话 GB 1080P MP4（字幕社招人内详）",
    "subtitle": "",
    "target": {
        "type": "电视剧",
        "cn_name": "金属口红",
        "en_name": "Metallic Rouge",
        "year": "",
        "part": "",
        "season": "S01",
        "episode": "E13",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "title": "Mai Xiang S01 2019 2160p WEB-DL H.265 DDP2.0-HHWEB",
    "subtitle": "麦香 | 全36集 | 4K | 类型:剧情/爱情/家庭 | 主演:傅晶/章呈赫/王伟/沙景昌/何音",
    "target": {
        "type": "电视剧",
        "cn_name": "麦香",
        "en_name": "Mai Xiang",
        "year": "2019",
        "part": "",
        "season": "S01",
        "episode": "",
        "restype": "WEB-DL",
        "pix": "2160p",
        "video_codec": "H265",
        "audio_codec": "DDP 2.0"
    }
}, {
    "path": "/volume1/电视剧/西部世界 第二季 (2016)/5.mkv",
    "target": {
        "type": "电视剧",
        "cn_name": "西部世界",
        "en_name": "",
        "year": "2016",
        "part": "",
        "season": "S02",
        "episode": "E05",
        "restype": "",
        "pix": "",
        "video_codec": "",
        "audio_codec": ""
    }
}, {
    "path": "/movies/The Vampire Diaries (2009) [tmdbid=18165]/The.Vampire.Diaries.S01E01.1080p.mkv",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "The Vampire Diaries",
        "year": "2009",
        "part": "",
        "season": "S01",
        "episode": "E01",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "",
        "tmdbid": 18165
    }
}, {
    "path": "/movies/Inception (2010) [tmdbid-27205]/Inception.2010.1080p.mkv",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "Inception",
        "year": "2010",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "",
        "tmdbid": 27205
    }
}, {
    "path": "/movies/Breaking Bad (2008) [tmdb=1396]/Season 1/Breaking.Bad.S01E01.1080p.mkv",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Breaking Bad",
        "year": "2008",
        "part": "",
        "season": "S01",
        "episode": "E01",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "",
        "tmdbid": 1396
    }
}, {
    "path": "/tv/Game of Thrones (2011) {tmdb=1399}/Season 1/Game.of.Thrones.S01E01.1080p.mkv",
    "target": {
        "type": "电视剧",
        "cn_name": "",
        "en_name": "Game Of Thrones",
        "year": "2011",
        "part": "",
        "season": "S01",
        "episode": "E01",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "",
        "tmdbid": 1399
    }
}, {
    "path": "/movies/Avatar (2009) {tmdb-19995}/Avatar.2009.1080p.mkv",
    "target": {
        "type": "未知",
        "cn_name": "",
        "en_name": "Avatar",
        "year": "2009",
        "part": "",
        "season": "",
        "episode": "",
        "restype": "",
        "pix": "1080p",
        "video_codec": "",
        "audio_codec": "",
        "tmdbid": 19995
    }
}]
