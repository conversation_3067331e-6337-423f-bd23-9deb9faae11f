# AI字幕自动生成(v2)

自动生成视频字幕，并使用大模型将字幕翻译成中文。

基于 [autosub](https://github.com/lightolly/MoviePilot-Plugins) 修改并适配v2，感谢原作者

## 功能特点

- 支持从视频音轨中提取字幕（使用faster-whisper）
- 支持从视频内嵌字幕中提取字幕
- 支持从外挂字幕文件中提取字幕
- 支持使用大模型（OpenAI）将字幕翻译成中文
- 支持批量翻译以提高效率
- 支持使用滑动窗口配置上下文提高翻译连贯性
- 支持多种字幕提取语言偏好设置
- 支持监听媒体入库事件自动执行字幕生成
- 支持手动触发字幕生成任务
- 支持任务队列机制，确保并发安全
- 支持任务状态列表展示（等待中 / 进行中 / 已完成 / 失败）

## 配置说明

### 基础配置

| 配置项      | 说明                     | 默认值    |
|----------|------------------------|--------|
| 启用插件     | 是否启用插件                 | 否      |
| 清除历史记录   | 清除已完成的任务记录（完成、跳过或失败）   | 否      |
| 媒体入库自动执行 | 监听到媒体入库事件后自动执行字幕生成     | 是      |
| 手动执行一次   | 保存配置后立即执行一次任务          | 否      |
| 发送通知     | 是否发送任务执行通知             | 否      |
| 文件大小（MB） | 最小处理的视频文件大小，小于该值的文件不处理 | 10     |
| 字幕源语言偏好  | 设置字幕提取的优先级策略           | 优先原音字幕 |
| 翻译为中文    | 是否使用大模型将字幕翻译成中文        | 是      |

### ASR配置（语音识别）

| 配置项                 | 说明               | 默认值  |
|---------------------|------------------|------|
| 允许从音轨提取字幕           | 是否允许从视频音轨中提取字幕   | 是    |
| faster-whisper 模型选择 | 使用的 Whisper 模型大小 | base |
| 使用代理下载模型            | 是否使用代理下载模型       | 是    |

### 翻译接口配置

> 可选使用 ChatGPT 插件配置 或 自定义 OpenAI 接口参数

| 配置项              | 说明                                   | 默认值                    |
|------------------|--------------------------------------|-------------------------|
| 复用ChatGPT插件配置   | 是否直接使用系统中已配置的 ChatGPT 插件参数       | 否                      |
| 使用代理服务器         | 是否通过 MP 配置的代理访问 OpenAI 接口       | 否                      |
| 兼容模式            | 是否启用兼容模式（绕过 `/v1` 路径拼接）         | 否                      |
| OpenAI API URL     | 自定义 OpenAI 接口地址                   | https://api.openai.com  |
| API 密钥           | OpenAI 的 API Key                     | 无                      |
| 自定义模型           | 使用的 LLM 模型名称（如 gpt-3.5-turbo） | gpt-3.5-turbo           |

### 翻译参数配置

| 配置项          | 说明                                 | 默认值 |
|---------------|------------------------------------|-----|
| 启用批量翻译      | 是否启用批量翻译以提高效率                      | 是   |
| 每批翻译行数      | 每批处理的字幕行数                         | 10  |
| 上下文窗口大小     | 翻译时考虑的上下文行数                       | 5   |
| LLM请求重试次数    | 翻译失败时的重试次数                        | 3   |
| 翻译英文时合并整句   | 对英文字幕先合并单词再翻译，提升翻译质量              | 否   |

### 手动运行配置

| 配置项  | 说明                    | 默认值 |
|------|-----------------------|-----|
| 媒体路径 | 要处理的媒体文件或文件夹绝对路径，每行一个 | 空   | 

## 字幕提取策略说明

字幕提取优先级：外挂字幕 > 内嵌字幕 > 音轨识别

字幕提取策略的选择主要取决于视频源语言和大模型的翻译能力。对于包含多语言字幕的非英语视频，建议根据以下原则选择策略：

1. 仅英文字幕
    - 仅使用英文字幕作为翻译源
    - 当视频无英文字幕时，使用ASR提取
    - 适用于大模型仅支持中英互译的场景

2. 优先英文字幕
    - 优先使用英文字幕作为翻译源
    - 无英文字幕时，使用其他语言字幕
    - 当所有字幕都不存在时，使用ASR提取
    - 适用于大模型在英译中任务上表现更好的场景

3. 优先原音字幕
    - 优先使用视频原始语言的字幕
    - 无原音字幕时，使用英文字幕
    - 当所有字幕都不存在时，使用ASR提取
    - 适用于大模型支持多语言翻译且翻译质量较好的场景

## 翻译方式说明

插件支持两种方式调用大模型进行翻译：

1. **复用 ChatGPT 插件配置**
   - 开启“复用ChatGPT插件配置”后，自动使用系统中维护的 ChatGPT 插件参数
   - 包括 API Key、API URL、是否使用代理等
   - 适合已有 ChatGPT 插件的用户快速部署

2. **自定义 OpenAI 接口参数**
   - 关闭“复用ChatGPT插件配置”后，可独立配置：
     - API 地址（支持反代）
     - API Key
     - 使用的模型
     - 是否使用代理
     - 是否启用兼容模式（避免 `/v1` 路径冲突）


---

## 注意事项

1. 翻译功能依赖大模型配置，使用前请确保已正确配置 OpenAI Key 或 ChatGPT 插件。
2. 首次使用音轨识别功能时，会自动从 HuggingFace 下载模型。开启"使用代理下载模型"选项会使用 MP 配置的代理。
3. 媒体路径支持单个文件或文件夹的绝对路径。选择文件夹时会递归处理其中的所有视频文件，外挂字幕将从媒体文件同级目录中查找。
4. 批量翻译通过一次处理多行字幕来减少 API 调用次数，提高效率。如果翻译结果与原文行数不匹配，系统会自动降级为逐行翻译。
5. 上下文窗口大小和批量翻译行数需要根据大模型的推理能力来调整。当模型能力不足时，过大的批量或上下文窗口可能会影响翻译质量。
6. 翻译后的中文字幕会打上“机翻”标签。
7. 插件运行时会启动一个后台线程用于消费任务队列，插件关闭时会清空队列并终止当前任务。

## todo

- 工作流/API接口
- 任务完成后调用媒体库刷新