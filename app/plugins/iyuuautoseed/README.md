# IYUU自动辅种

## 清除缓存后运行

将清理辅种成功或失败的种子缓存，完整跑完每个种子每个站点的辅种操作。

## 下载器说明

下载器为多选，选择多个下载器时，各个下载器的种子辅种互不影响

## 主辅分离说明

- 配置主辅分离后，下载器所有的种子辅种时都会将所辅的种子下载到主辅分离下载器中
- ⚠️注意主辅分离下载器不要配置到下载器中，这会导致主辅分离下载器中没有主下载器中的种子，重复将主下载中的种子下载到辅种下载器中。导致重复做种

## 辅种体积大小（GB）

种子文件大于该配置才会辅种

## 辅种站点

配置需要参与辅种的站点

## 不辅种标签

有该标签的种子会跳过不进行辅种

## 辅种后增加标签

辅种成功后会将该配置的标签加入到下载器中，多个标签使用`,`分隔

## 辅种后增加分类

- 辅种指定的分类，入启用了[分类复用](#分类复用)会优先使用该配置获取原种子的分类并设置
- 如下载器配置了`自动分类管理`建议启用[分类复用](#分类复用)避免空分类和原分类不同导致下载路径不同无法辅种

## 不辅种数据文件目录

不需要辅种的文件目录配置下载中的目录

## 将站点名添加到标签

可以将站点名称加入到标签中

## 跳过校验

- 仅QB下载器有效，跳过后可能会导致反复下载刷掉分享率、做假种的情况。本人仅仅测试了 QB `v4.6.6` 跳过无文件时不会自动开始，若有问题及时反馈将增加按钮选择是否需要跳过
- Tr下载器在4.0.x中属于默认行为

## 自动开始

跳过校验时有效

>QB开启跳过校验后需要手动开启自动开始后，种子才会自动开始，QB跳过可能会出现种子完整性误判，出现反复下载刷掉分享率和做假种的情况，请自行甄别。

## 分类复用

将复用原种子的分类，当原种子无分类时使用[辅种后增加分类](#辅种后增加分类)配置中的分类
